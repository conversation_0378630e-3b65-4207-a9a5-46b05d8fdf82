// Sistema de Blog - Logyc Contabilidade
class BlogSystem {
    constructor() {
        this.articles = this.getBlogData();
        this.currentCategory = 'all';
        this.searchTerm = '';
        this.currentPage = 1;
        this.articlesPerPage = 6;
        this.init();
    }
    
    init() {
        this.setupStyles();
        this.renderArticles();
        this.setupEventListeners();
    }
    
    getBlogData() {
        return [
            {
                id: 1,
                title: 'Como Abrir uma Empresa em 2024: <PERSON><PERSON><PERSON>',
                excerpt: 'Descubra o passo a passo completo para abrir sua empresa em 2024, desde a escolha do tipo societário até a obtenção do CNPJ.',
                content: 'Conteúdo completo do artigo sobre abertura de empresa...',
                category: 'abertura',
                author: 'Equi<PERSON>',
                date: '2024-01-15',
                readTime: '8 min',
                image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=250&fit=crop',
                tags: ['abertura', 'cnpj', 'empresa', 'documentos'],
                featured: true
            },
            {
                id: 2,
                title: 'MEI 2024: Tudo que Você Precisa Saber',
                excerpt: 'Limite de faturamento, atividades permitidas, obrigações e vantagens do MEI em 2024.',
                content: 'Conteúdo completo sobre MEI...',
                category: 'mei',
                author: 'Maria Silva',
                date: '2024-01-12',
                readTime: '6 min',
                image: 'https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=250&fit=crop',
                tags: ['mei', 'microempreendedor', 'faturamento', 'obrigações']
            },
            {
                id: 3,
                title: 'Simples Nacional: Vantagens e Como Aderir',
                excerpt: 'Entenda as vantagens do Simples Nacional e como sua empresa pode se beneficiar deste regime tributário.',
                content: 'Conteúdo sobre Simples Nacional...',
                category: 'fiscal',
                author: 'João Santos',
                date: '2024-01-10',
                readTime: '7 min',
                image: 'https://images.unsplash.com/photo-1554224154-26032fced8bd?w=400&h=250&fit=crop',
                tags: ['simples nacional', 'tributário', 'impostos', 'vantagens']
            },
            {
                id: 4,
                title: 'Departamento Pessoal: Obrigações Trabalhistas',
                excerpt: 'Conheça as principais obrigações trabalhistas que sua empresa deve cumprir em 2024.',
                content: 'Conteúdo sobre departamento pessoal...',
                category: 'pessoal',
                author: 'Ana Costa',
                date: '2024-01-08',
                readTime: '5 min',
                image: 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=400&h=250&fit=crop',
                tags: ['trabalhista', 'funcionários', 'obrigações', 'esocial']
            },
            {
                id: 5,
                title: '5 Dicas para Organizar a Contabilidade da sua Empresa',
                excerpt: 'Dicas práticas para manter a contabilidade da sua empresa sempre organizada e em dia.',
                content: 'Dicas de organização contábil...',
                category: 'dicas',
                author: 'Carlos Oliveira',
                date: '2024-01-05',
                readTime: '4 min',
                image: 'https://images.unsplash.com/photo-1554224154-22dec7ec8818?w=400&h=250&fit=crop',
                tags: ['organização', 'contabilidade', 'dicas', 'gestão']
            },
            {
                id: 6,
                title: 'Declaração de Imposto de Renda para Empresas',
                excerpt: 'Saiba como preparar sua empresa para a declaração do imposto de renda e evite problemas com a Receita.',
                content: 'Conteúdo sobre IR empresarial...',
                category: 'fiscal',
                author: 'Fernanda Lima',
                date: '2024-01-03',
                readTime: '9 min',
                image: 'https://images.unsplash.com/photo-1554224154-1696413565d3?w=400&h=250&fit=crop',
                tags: ['imposto de renda', 'declaração', 'receita federal', 'empresas']
            },
            {
                id: 7,
                title: 'Como Trocar de Contabilidade sem Complicações',
                excerpt: 'Passo a passo para trocar de contabilidade de forma segura e sem prejudicar sua empresa.',
                content: 'Guia para troca de contabilidade...',
                category: 'dicas',
                author: 'Roberto Mendes',
                date: '2024-01-01',
                readTime: '6 min',
                image: 'https://images.unsplash.com/photo-1554224154-26032fced8bd?w=400&h=250&fit=crop',
                tags: ['troca', 'contabilidade', 'transferência', 'documentos']
            },
            {
                id: 8,
                title: 'E-Social: Guia Completo para Empresas',
                excerpt: 'Tudo que você precisa saber sobre o e-Social e como adequar sua empresa a esta obrigação.',
                content: 'Guia completo do e-Social...',
                category: 'pessoal',
                author: 'Patricia Souza',
                date: '2023-12-28',
                readTime: '10 min',
                image: 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=400&h=250&fit=crop',
                tags: ['esocial', 'trabalhista', 'governo', 'obrigações']
            }
        ];
    }
    
    setupStyles() {
        const styles = `
            .blog-section {
                padding: 120px 0 80px;
                background: var(--cinza-claro, #f8f9fa);
                min-height: 100vh;
            }
            
            .blog-header {
                text-align: center;
                margin-bottom: 3rem;
            }
            
            .blog-header h1 {
                color: var(--azul-escuro, #005aec);
                font-size: 2.5rem;
                margin-bottom: 1rem;
            }
            
            .blog-header p {
                color: var(--cinza-escuro, #666);
                font-size: 1.1rem;
            }
            
            .blog-filters {
                display: flex;
                justify-content: center;
                flex-wrap: wrap;
                gap: 0.5rem;
                margin-bottom: 2rem;
            }
            
            .filter-btn {
                background: white;
                border: 1px solid #ddd;
                border-radius: 20px;
                padding: 8px 16px;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.3s;
                white-space: nowrap;
            }
            
            .filter-btn:hover,
            .filter-btn.active {
                background: var(--azul-escuro, #005aec);
                color: white;
                border-color: var(--azul-escuro, #005aec);
            }
            
            .blog-search {
                max-width: 400px;
                margin: 0 auto 3rem;
                display: flex;
                position: relative;
            }
            
            .blog-search input {
                flex: 1;
                padding: 12px 16px;
                border: 1px solid #ddd;
                border-radius: 25px;
                font-size: 16px;
                outline: none;
            }
            
            .blog-search button {
                position: absolute;
                right: 5px;
                top: 50%;
                transform: translateY(-50%);
                background: var(--azul-escuro, #005aec);
                color: white;
                border: none;
                border-radius: 50%;
                width: 35px;
                height: 35px;
                cursor: pointer;
            }
            
            .blog-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
                gap: 2rem;
                margin-bottom: 3rem;
            }
            
            .blog-card {
                background: white;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                transition: all 0.3s;
                cursor: pointer;
            }
            
            .blog-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            }
            
            .blog-card.featured {
                border: 2px solid var(--amarelo, #ffe206);
            }
            
            .blog-image {
                width: 100%;
                height: 200px;
                object-fit: cover;
            }
            
            .blog-content {
                padding: 1.5rem;
            }
            
            .blog-meta {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1rem;
                font-size: 0.9rem;
                color: var(--cinza-escuro, #666);
            }
            
            .blog-category {
                background: var(--azul-escuro, #005aec);
                color: white;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 0.8rem;
                text-transform: uppercase;
            }
            
            .blog-title {
                font-size: 1.3rem;
                font-weight: bold;
                color: var(--azul-escuro, #005aec);
                margin-bottom: 0.5rem;
                line-height: 1.4;
            }
            
            .blog-excerpt {
                color: var(--cinza-escuro, #666);
                line-height: 1.6;
                margin-bottom: 1rem;
            }
            
            .blog-footer {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding-top: 1rem;
                border-top: 1px solid #eee;
                font-size: 0.9rem;
                color: var(--cinza-escuro, #666);
            }
            
            .newsletter-section {
                background: var(--azul-escuro, #005aec);
                color: white;
                padding: 4rem 0;
                text-align: center;
            }
            
            .newsletter-content h2 {
                font-size: 2rem;
                margin-bottom: 1rem;
            }
            
            .newsletter-content p {
                font-size: 1.1rem;
                margin-bottom: 2rem;
                opacity: 0.9;
            }
            
            .newsletter-form {
                display: flex;
                max-width: 400px;
                margin: 0 auto;
                gap: 1rem;
            }
            
            .newsletter-form input {
                flex: 1;
                padding: 12px 16px;
                border: none;
                border-radius: 25px;
                font-size: 16px;
            }
            
            .newsletter-form button {
                background: var(--verde-claro, #01d800);
                color: white;
                border: none;
                border-radius: 25px;
                padding: 12px 24px;
                font-weight: bold;
                cursor: pointer;
                transition: background 0.3s;
            }
            
            .newsletter-form button:hover {
                background: var(--verde-escuro, #217345);
            }
            
            @media (max-width: 768px) {
                .blog-grid {
                    grid-template-columns: 1fr;
                }
                
                .blog-filters {
                    gap: 0.25rem;
                }
                
                .filter-btn {
                    font-size: 12px;
                    padding: 6px 12px;
                }
                
                .newsletter-form {
                    flex-direction: column;
                }
            }
        `;
        
        const styleSheet = document.createElement('style');
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
    }
    
    setupEventListeners() {
        // Filtros
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.currentCategory = e.target.dataset.category;
                this.currentPage = 1;
                this.renderArticles();
            });
        });
        
        // Busca
        const searchInput = document.getElementById('blogSearch');
        const searchBtn = document.getElementById('searchBtn');
        
        searchInput.addEventListener('input', (e) => {
            this.searchTerm = e.target.value.toLowerCase();
            this.currentPage = 1;
            this.renderArticles();
        });
        
        searchBtn.addEventListener('click', () => {
            this.renderArticles();
        });
        
        // Newsletter
        const newsletterForm = document.getElementById('newsletterForm');
        if (newsletterForm) {
            newsletterForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleNewsletterSubmit(e);
            });
        }
    }
    
    renderArticles() {
        const container = document.getElementById('blogGrid');
        const filteredArticles = this.filterArticles();
        const startIndex = (this.currentPage - 1) * this.articlesPerPage;
        const endIndex = startIndex + this.articlesPerPage;
        const articlesToShow = filteredArticles.slice(startIndex, endIndex);
        
        if (articlesToShow.length === 0) {
            container.innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; padding: 3rem;">
                    <h3>Nenhum artigo encontrado</h3>
                    <p>Tente usar outros termos de busca ou filtros.</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = articlesToShow.map(article => `
            <article class="blog-card ${article.featured ? 'featured' : ''}" onclick="this.openArticle(${article.id})">
                <img src="${article.image}" alt="${article.title}" class="blog-image" loading="lazy">
                <div class="blog-content">
                    <div class="blog-meta">
                        <span class="blog-category">${this.getCategoryName(article.category)}</span>
                        <span class="blog-date">${this.formatDate(article.date)}</span>
                    </div>
                    <h3 class="blog-title">${article.title}</h3>
                    <p class="blog-excerpt">${article.excerpt}</p>
                    <div class="blog-footer">
                        <span>Por ${article.author}</span>
                        <span>${article.readTime} de leitura</span>
                    </div>
                </div>
            </article>
        `).join('');
        
        this.renderPagination(filteredArticles.length);
    }
    
    filterArticles() {
        return this.articles.filter(article => {
            const matchesCategory = this.currentCategory === 'all' || article.category === this.currentCategory;
            const matchesSearch = !this.searchTerm || 
                article.title.toLowerCase().includes(this.searchTerm) ||
                article.excerpt.toLowerCase().includes(this.searchTerm) ||
                article.tags.some(tag => tag.includes(this.searchTerm));
            
            return matchesCategory && matchesSearch;
        });
    }
    
    renderPagination(totalArticles) {
        const container = document.getElementById('blogPagination');
        const totalPages = Math.ceil(totalArticles / this.articlesPerPage);
        
        if (totalPages <= 1) {
            container.innerHTML = '';
            return;
        }
        
        let paginationHTML = '<div class="pagination">';
        
        for (let i = 1; i <= totalPages; i++) {
            paginationHTML += `
                <button class="page-btn ${i === this.currentPage ? 'active' : ''}" 
                        onclick="window.blogSystem.goToPage(${i})">${i}</button>
            `;
        }
        
        paginationHTML += '</div>';
        container.innerHTML = paginationHTML;
    }
    
    goToPage(page) {
        this.currentPage = page;
        this.renderArticles();
        document.querySelector('.blog-section').scrollIntoView({ behavior: 'smooth' });
    }
    
    getCategoryName(category) {
        const names = {
            'mei': 'MEI',
            'abertura': 'Abertura',
            'fiscal': 'Fiscal',
            'pessoal': 'Pessoal',
            'dicas': 'Dicas'
        };
        return names[category] || category;
    }
    
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('pt-BR');
    }
    
    handleNewsletterSubmit(e) {
        const email = e.target.querySelector('input[type="email"]').value;
        
        if (window.notifications) {
            window.notifications.success('Email cadastrado com sucesso! Você receberá nossas novidades em breve.');
        }
        
        // Rastrear inscrição
        if (window.trackEvent) {
            window.trackEvent('newsletter_signup', { email_domain: email.split('@')[1] });
        }
        
        e.target.reset();
    }
}

// Adicionar método global para abrir artigos
window.openArticle = function(articleId) {
    if (window.trackEvent) {
        window.trackEvent('blog_article_click', { article_id: articleId });
    }
    
    // Por enquanto, mostrar notificação
    if (window.notifications) {
        window.notifications.info('Artigo completo em breve! Entre em contato para mais informações.');
    }
};

// Inicializar apenas na página do blog
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.pathname.includes('blog.html')) {
        window.blogSystem = new BlogSystem();
    }
});

// Exportar para uso global
window.BlogSystem = BlogSystem;
