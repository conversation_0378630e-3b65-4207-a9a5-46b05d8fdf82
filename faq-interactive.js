// FAQ Interativo com busca e analytics
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 FAQ Interativo carregado');
    
    // Elementos do DOM
    const faqItems = document.querySelectorAll('.faq-item');
    const searchInput = document.getElementById('faq-search');
    
    // Verificar se os elementos existem
    if (!faqItems.length) {
        console.log('ℹ️ Nenhum item FAQ encontrado na página');
        return;
    }
    
    console.log(`📋 ${faqItems.length} itens FAQ encontrados`);
    
    // Função para toggle do FAQ
    function toggleFAQ(item) {
        const isActive = item.classList.contains('active');
        
        // Fechar todos os outros itens
        faqItems.forEach(faqItem => {
            if (faqItem !== item) {
                faqItem.classList.remove('active');
            }
        });
        
        // Toggle do item atual
        if (isActive) {
            item.classList.remove('active');
            console.log('❌ FAQ fechado:', item.querySelector('h3').textContent);
        } else {
            item.classList.add('active');
            console.log('✅ FAQ aberto:', item.querySelector('h3').textContent);
            
            // Analytics - tracking de interação
            trackFAQInteraction(item);
        }
    }
    
    // Função de busca
    function searchFAQ(searchTerm) {
        const term = searchTerm.toLowerCase().trim();
        console.log('🔍 Buscando por:', term);
        
        let visibleCount = 0;
        
        faqItems.forEach(item => {
            const question = item.querySelector('h3').textContent.toLowerCase();
            const answer = item.querySelector('.faq-answer p').textContent.toLowerCase();
            const category = item.dataset.category || '';
            
            const matches = question.includes(term) || 
                          answer.includes(term) || 
                          category.includes(term);
            
            if (term === '' || matches) {
                item.style.display = 'block';
                visibleCount++;
                
                // Destacar termos encontrados
                if (term !== '') {
                    highlightSearchTerm(item, term);
                } else {
                    removeHighlight(item);
                }
            } else {
                item.style.display = 'none';
                item.classList.remove('active');
            }
        });
        
        console.log(`📊 ${visibleCount} itens encontrados para "${term}"`);
        
        // Mostrar mensagem se nenhum resultado
        showNoResultsMessage(visibleCount === 0 && term !== '');
    }
    
    // Função para destacar termos de busca
    function highlightSearchTerm(item, term) {
        const question = item.querySelector('h3');
        const answer = item.querySelector('.faq-answer p');
        
        [question, answer].forEach(element => {
            if (element) {
                const originalText = element.textContent;
                const regex = new RegExp(`(${term})`, 'gi');
                const highlightedText = originalText.replace(regex, '<mark>$1</mark>');
                
                if (highlightedText !== originalText) {
                    element.innerHTML = highlightedText;
                }
            }
        });
    }
    
    // Função para remover destaque
    function removeHighlight(item) {
        const question = item.querySelector('h3');
        const answer = item.querySelector('.faq-answer p');
        
        [question, answer].forEach(element => {
            if (element && element.innerHTML.includes('<mark>')) {
                element.innerHTML = element.textContent;
            }
        });
    }
    
    // Função para mostrar mensagem de "nenhum resultado"
    function showNoResultsMessage(show) {
        let noResultsMsg = document.querySelector('.no-results-message');
        
        if (show && !noResultsMsg) {
            noResultsMsg = document.createElement('div');
            noResultsMsg.className = 'no-results-message';
            noResultsMsg.innerHTML = `
                <div style="text-align: center; padding: 3rem; color: var(--gray-600);">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor" style="margin-bottom: 1rem; opacity: 0.5;">
                        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                    </svg>
                    <h3>Nenhum resultado encontrado</h3>
                    <p>Tente usar termos diferentes ou entre em contato conosco diretamente.</p>
                </div>
            `;
            document.querySelector('.faq-container').appendChild(noResultsMsg);
        } else if (!show && noResultsMsg) {
            noResultsMsg.remove();
        }
    }
    
    // Função de analytics
    function trackFAQInteraction(item) {
        const question = item.querySelector('h3').textContent;
        const category = item.dataset.category || 'geral';
        
        // Google Analytics (se disponível)
        if (typeof gtag !== 'undefined') {
            gtag('event', 'faq_interaction', {
                'event_category': 'FAQ',
                'event_label': question,
                'custom_parameter_category': category
            });
        }
        
        // Console para debug
        console.log('📊 Analytics - FAQ Interaction:', {
            question: question,
            category: category,
            timestamp: new Date().toISOString()
        });
    }
    
    // Event Listeners
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        if (question) {
            question.addEventListener('click', () => toggleFAQ(item));
        }
    });
    
    // Event listener para busca
    if (searchInput) {
        let searchTimeout;
        
        searchInput.addEventListener('input', function(e) {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                searchFAQ(e.target.value);
            }, 300); // Debounce de 300ms
        });
        
        // Limpar busca com Escape
        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                this.value = '';
                searchFAQ('');
                this.blur();
            }
        });
    }
    
    // Função de teste para debug
    window.testeFAQ = function() {
        console.log('🧪 Testando FAQ...');
        console.log('📋 Itens FAQ:', faqItems.length);
        console.log('🔍 Campo de busca:', searchInput ? 'Encontrado' : 'Não encontrado');
        
        if (faqItems.length > 0) {
            console.log('✅ Abrindo primeiro FAQ...');
            toggleFAQ(faqItems[0]);
            
            setTimeout(() => {
                console.log('🔍 Testando busca...');
                if (searchInput) {
                    searchInput.value = 'serviços';
                    searchFAQ('serviços');
                }
            }, 2000);
        }
    };
    
    console.log('✅ FAQ Interativo inicializado com sucesso');
    console.log('💡 Digite testeFAQ() no console para testar');
});

// Estilos CSS para o highlight de busca
const style = document.createElement('style');
style.textContent = `
    .faq-container mark {
        background: var(--accent-yellow);
        color: var(--gray-900);
        padding: 0.1em 0.2em;
        border-radius: 0.2em;
        font-weight: 600;
    }
    
    .no-results-message {
        animation: fadeIn 0.3s ease-out;
    }
`;
document.head.appendChild(style);
