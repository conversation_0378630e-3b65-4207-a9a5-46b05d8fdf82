// Calculadora de Mensalidade - Versão Corrigida
console.log('🧮 Calculadora carregada');

// Variável global para o tipo selecionado
let selectedType = '';

document.addEventListener('DOMContentLoaded', function() {
    console.log('📋 DOM carregado - Inicializando calculadora');
    
    // Elementos do DOM
    const companyTypes = document.querySelectorAll('.company-type');
    const calculationInputs = document.getElementById('calculationInputs');
    const faturamentoInput = document.getElementById('faturamento');
    const funcionariosInput = document.getElementById('funcionarios');
    const calcularBtn = document.getElementById('calcular');
    const resultadoDiv = document.getElementById('calculationResult');
    
    console.log('🔍 Elementos encontrados:', {
        companyTypes: companyTypes.length,
        calculationInputs: !!calculationInputs,
        faturamentoInput: !!faturamentoInput,
        funcionariosInput: !!funcionariosInput,
        calcularBtn: !!calcularBtn,
        resultadoDiv: !!resultadoDiv
    });
    
    // Event listeners para botões de tipo
    companyTypes.forEach(type => {
        type.addEventListener('click', function() {
            try {
                console.log('🎯 Tipo selecionado:', this.dataset.type);
                
                // Remove selected de todos os botões
                companyTypes.forEach(t => t.classList.remove('selected'));
                
                // Adiciona selected ao botão clicado
                this.classList.add('selected');
                
                // Armazena o tipo selecionado
                selectedType = this.dataset.type;
                
                // Mostra os inputs de cálculo
                if (calculationInputs) {
                    calculationInputs.style.display = 'block';
                    calculationInputs.scrollIntoView({ behavior: 'smooth' });
                }
                
                // Limpa resultado anterior
                if (resultadoDiv) {
                    resultadoDiv.style.display = 'none';
                }
                
                // Limpa campos
                if (faturamentoInput) faturamentoInput.value = '';
                if (funcionariosInput) funcionariosInput.value = '';
                
                // Foca no primeiro campo
                setTimeout(() => {
                    if (faturamentoInput) faturamentoInput.focus();
                }, 100);
                
                console.log('✅ Tipo definido como:', selectedType);
            } catch (error) {
                console.error('❌ Erro ao selecionar tipo:', error);
            }
        });
    });
    
    // Máscara para o campo de faturamento (mais flexível)
    if (faturamentoInput) {
        faturamentoInput.addEventListener('input', function(e) {
            try {
                let value = e.target.value;
                
                // Remove tudo que não é número, vírgula ou ponto
                value = value.replace(/[^\d.,]/g, '');
                
                // Atualiza o campo
                e.target.value = value;
                
                console.log('💰 Faturamento digitado:', value);
            } catch (error) {
                console.error('❌ Erro na máscara de faturamento:', error);
            }
        });
        
        // Permite colar valores
        faturamentoInput.addEventListener('paste', function(e) {
            setTimeout(() => {
                let value = e.target.value;
                value = value.replace(/[^\d.,]/g, '');
                e.target.value = value;
                console.log('📋 Valor colado e limpo:', value);
            }, 10);
        });
    }
    
    // Event listener para o botão calcular
    if (calcularBtn) {
        calcularBtn.addEventListener('click', function() {
            console.log('🔢 Iniciando cálculo...');
            calcularMensalidade();
        });
    }
    
    // Permite calcular com Enter nos campos
    [faturamentoInput, funcionariosInput].forEach(input => {
        if (input) {
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    calcularMensalidade();
                }
            });
        }
    });
    
    console.log('✅ Calculadora inicializada com sucesso');
});

// Função principal de cálculo
function calcularMensalidade() {
    try {
        console.log('🧮 Executando cálculo...');
        console.log('📊 Tipo selecionado:', selectedType);
        
        // Validar se tipo foi selecionado
        if (!selectedType) {
            alert('Por favor, selecione o tipo de empresa primeiro.');
            console.log('⚠️ Tipo não selecionado');
            return;
        }
        
        // Obter valores dos campos
        const faturamentoRaw = document.getElementById('faturamento').value.trim();
        const funcionariosRaw = document.getElementById('funcionarios').value.trim();
        
        console.log('📝 Valores brutos:', { faturamentoRaw, funcionariosRaw });
        
        // Validar faturamento
        if (!faturamentoRaw) {
            alert('Por favor, digite o faturamento médio mensal.');
            document.getElementById('faturamento').focus();
            return;
        }
        
        // Converter faturamento (aceita vírgula e ponto)
        let faturamento = parseFloat(faturamentoRaw.replace(/\./g, '').replace(',', '.'));
        
        if (isNaN(faturamento) || faturamento <= 0) {
            alert('Por favor, digite um valor válido para o faturamento.');
            document.getElementById('faturamento').focus();
            return;
        }
        
        // Converter funcionários
        let funcionarios = parseInt(funcionariosRaw) || 0;
        
        if (funcionarios < 0) {
            funcionarios = 0;
        }
        
        console.log('✅ Valores processados:', { faturamento, funcionarios, selectedType });
        
        // Calcular mensalidade
        const resultado = calcularValor(selectedType, faturamento, funcionarios);
        
        console.log('💰 Resultado calculado:', resultado);
        
        // Exibir resultado
        exibirResultado(resultado, faturamento, funcionarios);
        
    } catch (error) {
        console.error('❌ Erro no cálculo:', error);
        alert('Ocorreu um erro no cálculo. Tente novamente.');
    }
}

// Função de cálculo baseada no tipo
function calcularValor(tipo, faturamento, funcionarios) {
    console.log('🔢 Calculando para:', { tipo, faturamento, funcionarios });
    
    let valorBase = 0;
    let valorFuncionario = 0;
    
    switch (tipo) {
        case 'prestacao-servicos':
            valorBase = 150;
            valorFuncionario = 25;
            break;
        case 'comercio':
            valorBase = 180;
            valorFuncionario = 30;
            break;
        case 'industria':
            valorBase = 220;
            valorFuncionario = 35;
            break;
        case 'mei':
            valorBase = 75;
            valorFuncionario = 0;
            break;
        default:
            valorBase = 150;
            valorFuncionario = 25;
    }
    
    // Adicional por faturamento
    let adicionalFaturamento = 0;
    if (faturamento > 50000) {
        adicionalFaturamento = Math.floor((faturamento - 50000) / 10000) * 20;
    }
    
    // Cálculo final
    const valorFuncionarios = funcionarios * valorFuncionario;
    const valorTotal = valorBase + adicionalFaturamento + valorFuncionarios;
    
    const resultado = {
        valorBase,
        adicionalFaturamento,
        valorFuncionarios,
        valorTotal,
        tipo,
        faturamento,
        funcionarios
    };
    
    console.log('📊 Detalhes do cálculo:', resultado);
    
    return resultado;
}

// Função para exibir o resultado
function exibirResultado(resultado, faturamento, funcionarios) {
    const resultadoDiv = document.getElementById('calculationResult');
    
    if (!resultadoDiv) {
        console.error('❌ Div de resultado não encontrada');
        return;
    }
    
    const tipoNome = {
        'prestacao-servicos': 'Prestação de Serviços',
        'comercio': 'Comércio',
        'industria': 'Indústria',
        'mei': 'MEI'
    };
    
    const html = `
        <div class="result-card">
            <div class="result-header">
                <h3>💰 Sua Mensalidade Calculada</h3>
                <div class="monthly-value">R$ ${resultado.valorTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</div>
            </div>
            
            <div class="result-details">
                <h4>📋 Detalhamento:</h4>
                <div class="detail-item">
                    <span>Tipo de Empresa:</span>
                    <span>${tipoNome[resultado.tipo] || resultado.tipo}</span>
                </div>
                <div class="detail-item">
                    <span>Faturamento Mensal:</span>
                    <span>R$ ${faturamento.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</span>
                </div>
                <div class="detail-item">
                    <span>Funcionários:</span>
                    <span>${funcionarios}</span>
                </div>
                
                <hr>
                
                <div class="detail-item">
                    <span>Valor Base:</span>
                    <span>R$ ${resultado.valorBase.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</span>
                </div>
                ${resultado.adicionalFaturamento > 0 ? `
                <div class="detail-item">
                    <span>Adicional Faturamento:</span>
                    <span>R$ ${resultado.adicionalFaturamento.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</span>
                </div>
                ` : ''}
                ${resultado.valorFuncionarios > 0 ? `
                <div class="detail-item">
                    <span>Funcionários (${funcionarios} × R$ ${(resultado.valorFuncionarios/funcionarios).toFixed(2)}):</span>
                    <span>R$ ${resultado.valorFuncionarios.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</span>
                </div>
                ` : ''}
                
                <hr>
                
                <div class="detail-item total">
                    <span><strong>Total Mensal:</strong></span>
                    <span><strong>R$ ${resultado.valorTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</strong></span>
                </div>
            </div>
            
            <div class="result-actions">
                <button onclick="window.location.href='index.html#contato'" class="btn-contact">
                    📞 Solicitar Proposta Oficial
                </button>
                <button onclick="novoCalculo()" class="btn-recalculate">
                    🔄 Calcular Novamente
                </button>
            </div>
            
            <div class="result-note">
                <p><small>* Valores aproximados. A proposta oficial pode variar conforme análise detalhada da empresa.</small></p>
            </div>
        </div>
    `;
    
    resultadoDiv.innerHTML = html;
    resultadoDiv.style.display = 'block';
    resultadoDiv.scrollIntoView({ behavior: 'smooth' });
    
    console.log('✅ Resultado exibido com sucesso');
}

// Função para novo cálculo
function novoCalculo() {
    // Limpar seleção
    const companyTypes = document.querySelectorAll('.company-type');
    companyTypes.forEach(t => t.classList.remove('selected'));
    selectedType = '';
    
    // Esconder seções
    const calculationInputs = document.getElementById('calculationInputs');
    const resultadoDiv = document.getElementById('calculationResult');
    
    if (calculationInputs) calculationInputs.style.display = 'none';
    if (resultadoDiv) resultadoDiv.style.display = 'none';
    
    // Limpar campos
    document.getElementById('faturamento').value = '';
    document.getElementById('funcionarios').value = '';
    
    // Scroll para o topo
    window.scrollTo({ top: 0, behavior: 'smooth' });
    
    console.log('🔄 Calculadora resetada');
}

// Função de teste para debug
window.testeCalculadora = function() {
    console.log('🧪 Testando calculadora...');
    
    // Simular seleção de tipo
    selectedType = 'prestacao-servicos';
    console.log('✅ Tipo definido:', selectedType);
    
    // Simular preenchimento de campos
    document.getElementById('faturamento').value = '25000';
    document.getElementById('funcionarios').value = '3';
    console.log('✅ Campos preenchidos');
    
    // Executar cálculo
    setTimeout(() => {
        calcularMensalidade();
        console.log('✅ Teste concluído');
    }, 1000);
};

console.log('✅ Calculator-fixed.js carregado completamente');
console.log('💡 Digite testeCalculadora() no console para testar');
